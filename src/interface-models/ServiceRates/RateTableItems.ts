import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { isFuelSurchargeApplicable } from '@/helpers/RateHelpers/FuelSurchargeHelpers';
import { RateTypeObject } from '@/helpers/RateHelpers/RateTableItemHelpers';
import {
  getClientVariationPercent,
  getRateVariation,
} from '@/helpers/RateHelpers/RateVariationHelpers';
import { getServiceTypeById } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { ZoneToZoneRateType } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneToZoneRateType';
import rateMultipliers, {
  RateMultipliers,
} from '../Generic/ServiceTypes/RateMultipliers';
import serviceTypeRates, {
  ServiceTypeRates,
} from '../Generic/ServiceTypes/ServiceTypeRates';
import startAndReturnLegs, {
  StartAndReturnLegs,
} from '../Generic/StartAndReturnLegs/StartAndReturnLegs';
import TripRate from './ServiceTypes/TripRate/TripRate';
import UnitRate from './ServiceTypes/UnitRate/UnitRate';

export interface RateTableItemsInterface {
  rateTypeId: number;
  rateTypeObject?: RateTypeObject;
  serviceTypeId?: number;
  fuelSurcharge: boolean;
  originalRateTableMongoId: string;
}

export class RateTableItems implements RateTableItemsInterface {
  constructor(
    public rateTypeId: number = 0,
    public rateTypeObject?: RateTypeObject,
    public serviceTypeId?: number,
    public fuelSurcharge: boolean = false,
    public originalRateTableMongoId: string = '',
  ) {}

  /**
   * Check if fuel surcharge is applied for a client RateTableItem. This is used
   * to determine if we save the client FuelSurcharge object to the accounting
   * details.
   */
  get isClientFuelApplied(): boolean {
    return isFuelApplied(this, null);
  }

  /**
   * Check if fuel surcharge is applied for a driver RateTableItem. This is used
   * to determine if we save the driver FuelSurcharge object to the accounting
   * details.
   * @param clientFuelSurchargeRate - The client's fuel surcharge rate.
   */
  public isDriverFuelApplied(clientFuelSurchargeRate: number | null): boolean {
    return isFuelApplied(this, clientFuelSurchargeRate);
  }

  get rateLongName(): string {
    if (!this.rateTypeId) {
      return '';
    }
    return (
      serviceTypeRates.find(
        (rate: ServiceTypeRates) => rate.rateTypeId === this.rateTypeId,
      )?.longName || ''
    );
  }

  get serviceShortName(): string {
    if (!this.serviceTypeId) {
      return '';
    }
    const serviceDetails = getServiceTypeById(this.serviceTypeId);
    return serviceDetails ? serviceDetails.shortServiceTypeName : '';
  }
  get serviceLongName(): string {
    if (!this.serviceTypeId) {
      return '';
    }
    const serviceDetails = getServiceTypeById(this.serviceTypeId);
    return serviceDetails ? serviceDetails.longServiceTypeName : '';
  }

  get rateAmount(): string {
    if (!this.rateTypeObject) {
      return '';
    }

    let rate = 0;
    let multiplier = '';
    if (this.rateTypeId === 1) {
      rate = (this.rateTypeObject as TimeRateType).rate;
    } else if (this.rateTypeId === 6) {
      rate = (this.rateTypeObject as TripRate).rate;
    }

    const foundMultiplier = rateMultipliers.find(
      (multiplierItem: RateMultipliers) =>
        multiplierItem.id ===
        (this.rateTypeObject as TimeRateType).rateMultiplier,
    );

    if (foundMultiplier) {
      multiplier = 'p/' + foundMultiplier.shortName.toLowerCase();
    }

    return '$' + DisplayCurrencyValue(rate) + ' ' + multiplier;
  }

  get timeReturnLegs() {
    if (!this.rateTypeObject || this.rateTypeId !== 1) {
      return '';
    }

    const firstLeg = startAndReturnLegs.find(
      (leg: StartAndReturnLegs) =>
        leg.id === (this.rateTypeObject as TimeRateType).firstLegTypeId,
    );

    const lastLeg = startAndReturnLegs.find(
      (leg: StartAndReturnLegs) =>
        leg.id === (this.rateTypeObject as TimeRateType).lastLegTypeId,
    );

    const firstLegType = firstLeg ? firstLeg.longName : '';

    const lastLegType = lastLeg ? lastLeg.longName : '';

    return {
      firstLeg: firstLegType,
      lastLeg: lastLegType,
    };
  }

  /**
   * Retrieves the client adjustment percentage for a specific service and rate
   * type from a list of client service rate variations.
   * @param rateVariations - An array of `ClientServiceRateVariations` to search
   * within.
   * @returns The client adjustment percentage if found; otherwise, returns 0.
   * We will use this to calculate the percentage surcharge/discount for the
   * client for this rate type and service type.
   */
  public getRateVariation(
    rateVariations: ClientServiceRateVariations[] | null,
  ): ClientServiceRateVariations | undefined {
    if (
      !rateVariations ||
      rateVariations.length === 0 ||
      !this.rateTypeId ||
      !this.serviceTypeId
    ) {
      return;
    }
    return getRateVariation({
      rateTypeId: this.rateTypeId,
      serviceTypeId: this.serviceTypeId,
      rateVariations,
    });
  }

  /**
   * Retrieves the client adjustment percentage for a specific service and rate
   * type from a list of client service rate variations.
   * @param rateVariations - An array of `ClientServiceRateVariations` to search
   * within.
   * @returns The client adjustment percentage if found; otherwise, returns 0.
   * We will use this to calculate the percentage surcharge/discount for the
   * client for this rate type and service type.
   */
  public getClientVariation(
    rateVariations: ClientServiceRateVariations[] | null,
  ): number {
    if (
      !rateVariations ||
      rateVariations.length === 0 ||
      !this.rateTypeId ||
      !this.serviceTypeId
    ) {
      return 0;
    }
    return getClientVariationPercent({
      rateTypeId: this.rateTypeId,
      serviceTypeId: this.serviceTypeId,
      rateVariations,
    });
  }
}

export default RateTableItems;

/**
 * Helper function to check if fuel surcharge is applied.
 * @param clientFuelSurchargeRate - The client's fuel surcharge rate.
 * @returns {boolean} - True if fuel surcharge is applied, false otherwise.
 */
function isFuelApplied(
  rateTableItem: RateTableItems,
  clientFuelSurchargeRate: number | null,
): boolean {
  const { rateTypeId, rateTypeObject, fuelSurcharge } = rateTableItem;
  if (!rateTypeObject) {
    return false;
  }

  // Check if rateTypeObject is a list
  // ZoneRateType (rateTypeId 2), PointToPointRateType (rateTypeId 4), UnitRate (rateTypeId 5)
  if (Array.isArray(rateTypeObject)) {
    // Check if any of the rates in the list have fuel surcharge applied. If
    // at least one has fuel surcharge applied, return true so the
    // FuelSurcharge object is saved to the accounting details for use in the calculations.
    const rto:
      | ZoneRateType[]
      | PointToPointRateType[]
      | UnitRate[]
      | ZoneToZoneRateType[] = rateTypeObject;
    return rto.some((rate) =>
      isFuelSurchargeApplicable(
        rate.isFuelSurchargeApplied,
        clientFuelSurchargeRate,
      ),
    );
  } else if (rateTypeId === 1 || rateTypeId === 4) {
    // For time rate and point to point rate, use the appliedFuelSurchargeId
    const rto = rateTypeObject as TimeRateType | PointToPointRateType;
    return isFuelSurchargeApplicable(
      rto.appliedFuelSurchargeId,
      clientFuelSurchargeRate,
    );
  } else {
    // For trip/Quoted rate (and any other fall through cases), use the fuelSurcharge
    // property
    return fuelSurcharge;
  }
}

// export type RateTableItemsInterface =
//   | {
//       rateTypeId: 1;
//       rateTypeObject: TimeRateType;
//       serviceTypeId?: number;
//       fuelSurcharge: boolean;
//     }
//   | {
//       rateTypeId: 2;
//       rateTypeObject: ZoneRateType[];
//       serviceTypeId?: number;
//       fuelSurcharge: boolean;
//     }
//   | {
//       rateTypeId: 3;
//       rateTypeObject: DistanceRateType[];
//       serviceTypeId?: number;
//       fuelSurcharge: boolean;
//     }
//   | {
//       rateTypeId: 4;
//       rateTypeObject: PointToPointRateType | PointToPointRateType[];
//       serviceTypeId?: number;
//       fuelSurcharge: boolean;
//     }
//   | {
//       rateTypeId: 5;
//       rateTypeObject: UnitRate[];
//       serviceTypeId?: number;
//       fuelSurcharge: boolean;
//     }
//   | {
//       rateTypeId: 6;
//       rateTypeObject: TripRate;
//       serviceTypeId?: number;
//       fuelSurcharge: boolean;
//     };
