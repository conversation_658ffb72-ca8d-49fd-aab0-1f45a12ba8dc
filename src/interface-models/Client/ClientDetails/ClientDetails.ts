import { ClientRateExpirationSummaries } from '@/interface-models/Client/ClientDetails/ClientRateExpirationSummary';
import ClientReferenceTypes from '@/interface-models/Client/ClientDetails/References/ClientReferenceTypes';
import { NationalClientDetails } from '@/interface-models/Client/NationalClientDetails';
import AccountsReceivable from '@/interface-models/Generic/AccountsReceivable/AccountsReceivable';
import { AddressApplicationType } from '@/interface-models/Generic/AddressApplicationType';
import { AddressAU } from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import { POBoxAddressAU } from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/POBoxAddressAU';
import Communication from '@/interface-models/Generic/Communication/Communication';
import DefaultEmail from '@/interface-models/Generic/DefaultEmail/DefaultEmail';
import ProofOfDelivery from '@/interface-models/Generic/ProofOfDelivery/ProofOfDelivery';
import ReferralSource from '@/interface-models/Generic/ReferralSource/ReferalSource';
import {
  WeightRequirement,
  weightRequirements,
} from '@/interface-models/Jobs/WeightRequirement/WeightRequirement';
import DefaultsConfiguration from '@/interface-models/ServiceRates/DefaultsConfiguration/DefaultsConfiguration';
import { RateExpirationSummary } from '@/interface-models/ServiceRates/RateExpirationSummary';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { sessionManager } from '@/store/session/SessionState';
import { SendEmailsConfig } from './SendEmailsConfig';
export interface ClientDetailsInterface {
  _id?: string;
  clientId?: string | undefined;
  clientName: string;
  tradingName: string;
  locationAddress: AddressAU | undefined;
  billingAddress: AddressAU;
  poBoxAddress: POBoxAddressAU | null;
  companyShortId: string;
  companyDivisionShortId: string;
  nationalClientId?: string;
  clientCommencementDate: number;
  industryTypes: number[];
  abn: string;
  acn: string;
  countryCode: string;
  invoiceTemplateId: string;
  billingCycleId: number;
  clientPersonDispatchers: string[];
  preAssignedVehicleDetails: string[];
  preAssignedDriverIds: string[];
  avoidTolls: boolean;
  clientTradingTerms: number;
  additionalEquipments: number[];
  specialInstructions: Communication[];
  references: ClientReferenceTypes;
  statusList: number[];
  defaultsConfiguration: DefaultsConfiguration[];
  entityType: string;
  transportCompany: boolean;
  proprietorName: string;
  proprietorLocationAddress: AddressAU;
  establishedYear: number | null;
  referralSource: ReferralSource;
  clientSignupDate: number | null;
  proofOfDelivery: ProofOfDelivery;
  accountsReceivable: AccountsReceivable;
  defaultEmails: DefaultEmail[];
  clientType: number;
  expiredFuelSurchargeDefaultsToDivisionRate: boolean;
  importTransformationType?: string | null;
  defaultDispatcherId: string;
  weightRequirementId: number | null;
  sendEmailsConfig: SendEmailsConfig;
  expiredServiceRateDefaultsToDivisionRate: boolean;
  rateExpirationSummaries: ClientRateExpirationSummaries | null;
  pickupLoadDuration: number | null;
  dropoffLoadDuration: number | null;
  usesStandardDivisionRates: boolean | null;
}

export class ClientDetails implements ClientDetailsInterface {
  private _nationalClientName?: string | undefined;
  constructor(
    public _id?: string,
    public clientId?: string | undefined,
    public clientName: string = '',
    public tradingName: string = '',
    public locationAddress: AddressAU = new AddressAU(),
    public billingAddress: AddressAU = new AddressAU(),
    public poBoxAddress: POBoxAddressAU | null = null,
    public companyShortId: string = sessionManager.getCompanyId(),
    public companyDivisionShortId: string = sessionManager.getDivisionId(),
    public nationalClientId?: string,
    public clientCommencementDate: number = new Date().getTime(),
    public industryTypes: number[] = [],
    public abn: string = '',
    public acn: string = '',
    public countryCode: string = 'AU',
    public invoiceTemplateId: string = '',
    public billingCycleId: number = 1,
    public clientPersonDispatchers: string[] = [],
    public preAssignedVehicleDetails: string[] = [],
    public preAssignedDriverIds: string[] = [],
    public avoidTolls: boolean = false,
    public clientTradingTerms: number = 2,
    public additionalEquipments: number[] = [],
    public specialInstructions: Communication[] = [],
    public references: ClientReferenceTypes = new ClientReferenceTypes(),
    public statusList: number[] = [7],
    public defaultsConfiguration: DefaultsConfiguration[] = [],
    public entityType: string = '',
    public transportCompany: boolean = false,
    public proprietorName: string = '',
    public proprietorLocationAddress: AddressAU = new AddressAU(),
    public referralSource: ReferralSource = new ReferralSource(),
    public establishedYear: number | null = null,
    public clientSignupDate: number | null = null,
    public proofOfDelivery: ProofOfDelivery = new ProofOfDelivery(),
    public accountsReceivable: AccountsReceivable = new AccountsReceivable(),
    public defaultEmails: DefaultEmail[] = [],
    public clientType: number = 1,
    public expiredFuelSurchargeDefaultsToDivisionRate: boolean = true,
    public importTransformationType?: string | null,
    public defaultDispatcherId: string = '',
    public weightRequirementId: number | null = 7,
    public sendEmailsConfig: SendEmailsConfig = new SendEmailsConfig(),
    public expiredServiceRateDefaultsToDivisionRate: boolean = true,
    public rateExpirationSummaries: ClientRateExpirationSummaries | null = null,
    public pickupLoadDuration: number | null = null,
    public dropoffLoadDuration: number | null = null,
    public usesStandardDivisionRates: boolean | null = true,
  ) {}

  get displayName(): string {
    return this.tradingName ? this.tradingName : this.clientName;
  }

  get isCashSale(): boolean {
    return this.clientId === 'CS';
  }
  set isCashSale(value: boolean) {
    this.clientId = 'CS';
    this.clientName = 'CASH SALES';
  }

  // A client is RETIRED if clientDetails.statusList includes 13
  // A client is PENDING if clientDetails.statusList includes 3
  // A client is SEE ACCOUNTS if clientDetails.statusList includes 7

  // Find and return the clients weight requirement. Utilised for validating
  // weight requirement.
  get weightRequirement(): WeightRequirement | null {
    const weightRequirementId: number | null = this.weightRequirementId;
    const weightRequirement = weightRequirements.find(
      (x: WeightRequirement) => x.id === weightRequirementId,
    );
    return weightRequirement ? weightRequirement : null;
  }
  // Adds or removes status of 3 for PENDING from statusList
  get isPlaceholder(): boolean {
    return !this._id && this.statusList.includes(3);
  }
  set isPlaceholder(isPlaceholderClient: boolean) {
    if (isPlaceholderClient) {
      this.statusList.push(3);
      return;
    }
    const pendingIndex = this.statusList.findIndex((x: number) => x === 3);
    this.statusList.splice(pendingIndex, 1);
  }

  get nationalClientName(): string {
    if (!!this.nationalClientId && this._nationalClientName === undefined) {
      this.setNationalClientName();
    }
    const name = this._nationalClientName ?? '';
    return name;
  }

  public async setNationalClientName(): Promise<void> {
    if (this.nationalClientId) {
      this._nationalClientName =
        (await this.returnNationalClientDetails())?.name ?? '';
    }
  }

  public async returnNationalClientDetails(): Promise<NationalClientDetails | null> {
    if (!this.nationalClientId) {
      return null;
    }
    return useClientDetailsStore().requestNationalClientDetailsById(
      this.nationalClientId,
    );
  }

  // Finds and update the type of billing address that should be applied to the client
  public setBillingAddress() {
    let type = AddressApplicationType.SAME_AS_COMPANY;
    if (
      (!this.billingAddress || !this.billingAddress.addressId) &&
      !this.poBoxAddress
    ) {
      type = AddressApplicationType.SAME_AS_COMPANY;
    } else if (
      this.billingAddress &&
      this.billingAddress.formattedAddress &&
      this.billingAddress.formattedAddress ===
        this.locationAddress.formattedAddress
    ) {
      type = AddressApplicationType.SAME_AS_COMPANY;
    } else if (
      this.billingAddress &&
      this.billingAddress.formattedAddress &&
      this.billingAddress.formattedAddress !==
        this.locationAddress.formattedAddress
    ) {
      type = AddressApplicationType.BILLING_ADDRESS;
    } else if (this.poBoxAddress !== null) {
      type = AddressApplicationType.PO_BOX;
    }

    return this.updateBillingAddress(type);
  }

  // Update clients billing address based on the addressApplicationType
  public updateBillingAddress(type: AddressApplicationType) {
    switch (type) {
      case AddressApplicationType.SAME_AS_COMPANY:
        this.billingAddress = this.locationAddress;
        this.poBoxAddress = null;
        break;
      case AddressApplicationType.BILLING_ADDRESS:
        this.billingAddress = this.billingAddress
          ? Object.assign(new AddressAU(), this.billingAddress)
          : new AddressAU();
        this.poBoxAddress = null;
        break;
      case AddressApplicationType.PO_BOX:
        this.billingAddress = new AddressAU();
        this.poBoxAddress = this.poBoxAddress
          ? Object.assign(new POBoxAddressAU(), this.poBoxAddress)
          : new POBoxAddressAU();
        break;
    }
    return type;
  }

  public async save(): Promise<ClientDetails | null> {
    this.companyDivisionShortId = this.companyDivisionShortId
      ? this.companyDivisionShortId
      : sessionManager.getDivisionId();
    this.companyShortId = this.companyShortId
      ? this.companyShortId
      : sessionManager.getCompanyId();

    this.defaultEmails =
      this.defaultEmails && this.defaultEmails.length
        ? this.defaultEmails.filter((e) => !!e.email)
        : [];

    // we should confirm that the billing address is set correctly. This is because
    // the action for setting the billing address is done on the selection of the
    // "BILLING ADDRESS IS" input in the client maintenance screen. If the clients data
    // was imported the user most likely never actions the select on the input. Running this
    // setBillingAddress allows us to set the correct billing address without requiring the
    // users actions on the input.
    this.setBillingAddress();

    return useClientDetailsStore().saveClientDetails(this);
  }

  // Combine all values om expirationSummaries into a single list, if they're
  // not null. Used in RateExpirationSummaryAlert component.
  get allRateExpirationSummaries(): RateExpirationSummary[] {
    const foundSummary = useClientDetailsStore().clientSummaryList.find(
      (c) => c.clientId === this.clientId,
    );
    // Use summary version if it's present in the store, as this will be the
    // most up to date
    const rateExpirationSummaries = foundSummary
      ? foundSummary.rateExpirationSummaries
      : this.rateExpirationSummaries;
    if (!rateExpirationSummaries) {
      return [];
    }
    // Combine to single list, adding prefixes to names to be more descriptive
    // in the table
    return [
      rateExpirationSummaries.serviceRateSummary
        ? {
            ...rateExpirationSummaries.serviceRateSummary,
            name:
              'Rate Card: ' +
              (rateExpirationSummaries.serviceRateSummary.name
                ? rateExpirationSummaries.serviceRateSummary.name
                : 'None Active'),
          }
        : null,
      rateExpirationSummaries.fuelSurchargeSummary
        ? {
            ...rateExpirationSummaries.fuelSurchargeSummary,
            name:
              'Fuel Surcharge: ' +
              (rateExpirationSummaries.fuelSurchargeSummary.name
                ? rateExpirationSummaries.fuelSurchargeSummary.name
                : 'None Active'),
          }
        : null,

      rateExpirationSummaries.serviceRateVariationsSummary
        ? {
            ...rateExpirationSummaries.serviceRateVariationsSummary,
            name:
              'Rate Variations: ' +
              (rateExpirationSummaries.serviceRateVariationsSummary.name,
              rateExpirationSummaries.serviceRateVariationsSummary.validToDate
                ? 'Expiring Soon'
                : 'None Active'),
          }
        : null,

      rateExpirationSummaries.defaultsConfigurationSummary
        ? {
            ...rateExpirationSummaries.defaultsConfigurationSummary,
            name:
              'Default Rate Card: ' +
              (rateExpirationSummaries.defaultsConfigurationSummary.validToDate
                ? 'Expiring Soon'
                : 'None Active'),
          }
        : null,
    ].filter((x) => !!x) as RateExpirationSummary[];
  }
}

export default ClientDetails;
