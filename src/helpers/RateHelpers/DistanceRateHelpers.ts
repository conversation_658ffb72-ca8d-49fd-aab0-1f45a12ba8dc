import {
  addPercentageTo,
  calculateGstInclusiveTotals,
  getPercentageOf,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import { roundValueByGraceType } from '@/helpers/RateHelpers/GraceTypeHelpers';
import { DistanceRateTravelSummary } from '@/interface-models/Generic/Accounting/AdditionalAccountingData';
import { returnGraceLongName } from '@/interface-models/Generic/ServiceTypes/GraceTypes';
import { StartAndReturnLegsEnum } from '@/interface-models/Generic/StartAndReturnLegs/StartAndReturnLegs';
import { AdditionalTravel } from '@/interface-models/Jobs/FinishedJobDetails/DistanceRate/AdditionalTravel';
import { DistanceRateData } from '@/interface-models/Jobs/FinishedJobDetails/DistanceRate/DistanceRateData';
import { DistanceRateRangeSubtotal } from '@/interface-models/Jobs/FinishedJobDetails/DistanceRate/DistanceRateRangeSubtotal';
import { LegDuration } from '@/interface-models/Jobs/LegDuration';
import { ChargeBasis } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/ChargeBasis';
import DistanceRateType from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/DistanceRateType';
import { MinChargeBasis } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/MinChargeBasis';
import { RangedRate } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RangedRate';
import { RateBracketType } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RateBracketType';

/**
 * Summarizes a list of range rates, such that we can it in the UI.
 *
 * @param {RangedRate[]} rates - Array of range rate objects.
 * @returns {string} Summary string of the range rates. Returns 'Invalid' if
 * rates are empty or null, 'Not set' if a single rate has no value, or a
 * summary of the rates.
 */
export function returnRangeRateListSummary(
  rates: RangedRate[],
  variancePct: number | null | undefined,
): string {
  if (!rates || rates.length === 0) {
    return 'Invalid';
  }
  if (rates.length === 1 && !rates[0].rate) {
    return 'Not set';
  }

  if (rates.length <= 2) {
    return rates
      .map((rate, index) => {
        const rateToApply = addPercentageTo(rate.rate, variancePct ?? 0);
        return `${returnRangeRateSummary(rate, index)}: $${rateToApply}/km`;
      })
      .join(', ');
  }
  // Get the first and last dollar rates with variance percentage applied
  const firstRate = addPercentageTo(rates[0].rate, variancePct ?? 0);
  const lastRate = addPercentageTo(
    rates[rates.length - 1].rate,
    variancePct ?? 0,
  );
  const firstRateSummary = `${returnRangeRateSummary(
    rates[0],
    0,
  )}: $${firstRate}/km`;
  const lastRateSummary = `${returnRangeRateSummary(
    rates[rates.length - 1],
    rates.length - 1,
  )}: $${lastRate}/km`;

  return `${firstRateSummary}, ..., ${lastRateSummary}`;
}

/**
 * Generates a summary string for a given range rate.
 *
 * @param {RangedRate} rate - The rate object containing bracketMin and bracketMax values.
 * @param {number} index - The index of the rate in the list of rates.
 * @returns {string} A summary string describing the range rate.
 *
 * The function returns:
 * - 'Invalid' if the rate is null or if bracketMin is greater than bracketMax (when bracketMax is not -1).
 * - 'Any distance' if the index is 0 and bracketMax is -1.
 * - 'Less than {bracketMax}km' if the index is 0 and bracketMax is not -1.
 * - 'Over {bracketMin}km' if bracketMax is -1.
 * - '{bracketMin} to {bracketMax}km' for all other cases.
 */
export function returnRangeRateSummary(
  rate: RangedRate,
  index: number,
): string {
  if (!rate || (rate.bracketMax !== -1 && rate.bracketMin > rate.bracketMax)) {
    return 'Invalid';
  }
  if (index === 0 && rate.bracketMax === -1) {
    return `Any distance`;
  } else if (index === 0 && rate.bracketMax !== -1) {
    return `Less than ${rate.bracketMax}km`;
  } else if (rate.bracketMax === -1) {
    return `Over ${rate.bracketMin}km`;
  } else {
    return `${rate.bracketMin} to ${rate.bracketMax}km`;
  }
}

/**
 * Returns a description of the minimum charge based on the distance rate.
 *
 * @param distanceRate - The distance rate object containing the minimum charge
 * information.
 * @returns A string representing the minimum charge description. If the minimum
 *          charge basis is an amount, it returns the charge prefixed with a
 *          dollar sign. If the minimum charge basis is distance, it returns the
 *          charge followed by 'km'.
 */
export function returnMinimumChargeDescription(distanceRate: DistanceRateType) {
  if (distanceRate.minChargeBasis === MinChargeBasis.AMOUNT) {
    return `$${distanceRate.minCharge}`;
  } else {
    return `${distanceRate.minCharge}km`;
  }
}

/**
 * Generates a description for the charge increment based on the distance rate.
 *
 * @param distanceRate - The distance rate object containing charge increment
 * and grace type information.
 * @returns A string describing the charge increment and grace type.
 */
export function returnChargeIncrementDescription(
  distanceRate: DistanceRateType,
) {
  return `Every ${distanceRate.chargeIncrement}km (${returnGraceLongName(
    distanceRate.graceType,
  )})`;
}

/**
 * Generates the rate data for a distance rate job, based on the provided
 * DistanceRateType object and other parameters.
 *
 * @param travelDistance - The raw travel distance from the planned route or GPS
 * data
 * @param distanceRate - The rateTypeObject from the RateTableItems, containing
 * all the details for a distance rate type job
 * @param additionalDistances - The additional distances (in km) for the job,
 * including depotToFirstLeg, depotToLastLeg, returnToFirstPud
 * @param additionalDurations - The additional durations (in ms) for the job,
 * including depotToFirstLeg, depotToLastLeg, returnToFirstPud.
 * @param editedTravelDistance - The edited travel distance, if the user has
 * manually changed the distance. This will be undefined the first time the rate
 * data is calculated, indicating that we should use the raw travel distance.
 * Every time after that, the editedTravelDistance will be the value used to
 * calculate the rate data.
 * @param isGstRegistered - A boolean indicating whether the entity is GST
 * registered. Defaults to true.
 * @param variancePct - The percentage variance to apply to the rates, if any.
 * Applies to the following fields
 *  - baseFreightCharge
 *  - minCharge
 *  - all brackets in the distanceRate.rates array.
 *  - demurrage (based on accounting custom config)
 *
 * @returns - A DistanceRateData object containing all the calculated data for
 */
export function generateDistanceRateData({
  travelDistance,
  distanceRate,
  additionalDistances,
  additionalDurations,
  editedTravelDistance,
  isGstRegistered = true,
  variancePct,
}: {
  travelDistance: number;
  distanceRate: DistanceRateType;
  additionalDistances: LegDuration;
  additionalDurations: LegDuration;
  editedTravelDistance?: number;
  isGstRegistered?: boolean;
  variancePct: number | null | undefined;
}): DistanceRateData | null {
  try {
    if (!distanceRate?.rates?.length) {
      throw new Error('Invalid distance rate - no rate ranges are set.');
    }
    // Calculate any additional travel distances pre and post job, based on the
    // firstLegTypeId and lastLegTypeId
    const additionalTravelPreJob = generateAdditionalTravel(
      'PRE',
      distanceRate.firstLegTypeId,
      additionalDistances,
      additionalDurations,
    );
    const additionalTravelPostJob = generateAdditionalTravel(
      'POST',
      distanceRate.lastLegTypeId,
      additionalDistances,
      additionalDurations,
    );

    // If editedTravelDistance is set and different from the travelDistance, use
    // that. Otherwise, use the raw travelDistance
    const distanceToApply = RoundCurrencyValue(
      editedTravelDistance ?? travelDistance,
      3,
    );
    // Adjust the distance by the additional travel distances pre and post job
    let adjustedDistance = RoundCurrencyValue(
      distanceToApply +
        additionalTravelPreJob.distanceInKm +
        additionalTravelPostJob.distanceInKm,
      3,
    );

    // First we need to round the distance to the nearest increment, based on the graceType
    adjustedDistance = roundValueByGraceType(
      adjustedDistance,
      distanceRate.chargeIncrement,
      distanceRate.graceType,
    );
    adjustedDistance = RoundCurrencyValue(adjustedDistance, 3);

    // If the minChargeBasis is set to DISTANCE, we need to ensure the adjusted
    // distance is at least the minimum charge
    if (distanceRate.minChargeBasis === MinChargeBasis.DISTANCE) {
      // Get the minimum distance with the variance percentage applied
      const minDistance = addPercentageTo(
        distanceRate.minCharge,
        variancePct ?? 0,
      );

      // If the adjusted distance is less than the minimum distance, set it to
      // the minimum distance
      adjustedDistance = Math.max(adjustedDistance, minDistance);
    }

    const rangeSubtotals: DistanceRateRangeSubtotal[] =
      generateDistanceRangeSubtotals(
        adjustedDistance,
        distanceRate,
        isGstRegistered,
        variancePct,
      );

    // Get the gst breakdown of the base freight charge
    const baseFreightCharge = calculateGstInclusiveTotals(
      addPercentageTo(distanceRate.baseFreightCharge ?? 0, variancePct ?? 0),
      isGstRegistered,
    );

    // Add the base freight to the summed range subtotals
    let totalExclGst =
      baseFreightCharge.exclGst +
      rangeSubtotals.reduce(
        (total, subtotal) => total + subtotal.chargeExclGst,
        0,
      );
    let totalGst = isGstRegistered
      ? baseFreightCharge.gst +
        rangeSubtotals.reduce(
          (total, subtotal) => total + subtotal.chargeGst,
          0,
        )
      : 0;
    let total =
      baseFreightCharge.total +
      rangeSubtotals.reduce(
        (total, subtotal) => total + subtotal.chargeTotal,
        0,
      );

    // If the minChargeBasis is set to AMOUNT, we need to ensure the total
    // charge is at least the minimum charge
    if (distanceRate.minChargeBasis === MinChargeBasis.AMOUNT) {
      // Get the minimum distance with the variance percentage applied
      const minChargeDollars = addPercentageTo(
        distanceRate.minCharge,
        variancePct ?? 0,
      );

      // If the total charge is less than the minimum charge, set it to the
      // minimum charge
      if (totalExclGst < minChargeDollars) {
        const updatedTotals = calculateGstInclusiveTotals(
          minChargeDollars,
          isGstRegistered,
        );
        totalExclGst = updatedTotals.exclGst;
        totalGst = updatedTotals.gst;
        total = updatedTotals.total;
      }
    }

    // If editedTravelDistance is set, set it back to itself. Otherwise, set
    // it to the initial distance so it's synced with the uneditedTravelDistance
    return {
      rangeSubtotals,
      baseFreightSubtotal: baseFreightCharge,
      preJobTravel: additionalTravelPreJob,
      postJobTravel: additionalTravelPostJob,
      uneditedTravelDistance: RoundCurrencyValue(travelDistance, 3),
      editedTravelDistance: distanceToApply,
      calculatedTotalDistance: adjustedDistance,
      chargeExclGst: RoundCurrencyValue(totalExclGst),
      chargeGst: RoundCurrencyValue(totalGst),
      chargeTotal: RoundCurrencyValue(total),
    };
  } catch (error) {
    if (error instanceof Error && error.message) {
      logConsoleError(error.message, error);
    } else {
      logConsoleError(
        'An error occurred while generating distance rate data',
        error,
      );
    }
    return null;
  }
}

/**
 * Generates the range subtotals for a distance rate job, based on the provided
 * distance and distance rate object. A different calculation is used based on
 * the RateBracketType set in the distanceRate object.
 *
 * @param distance - The total distance to calculate the subtotals for.
 * @param distanceRate - The rate structure containing the distance brackets and
 * corresponding rates.
 * @returns
 */
function generateDistanceRangeSubtotals(
  distance: number,
  distanceRate: DistanceRateType,
  isGstRegistered: boolean,
  variancePct: number | null | undefined,
): DistanceRateRangeSubtotal[] {
  if (distanceRate.rateBracketType === RateBracketType.PROGRESSIVE) {
    return generateProgressiveRangeSubtotals(
      distance,
      distanceRate,
      isGstRegistered,
      variancePct,
    );
  } else {
    return generateAbsoluteRangeSubtotals(
      distance,
      distanceRate,
      isGstRegistered,
      variancePct,
    );
  }
}

/**
 * Used to calculate the subtotals for a distance rate job, where the
 * RateBracketType is set to PROGRESSIVE, meaning that for each bracket in
 * distanceRate.rates, the portion of the distance that falls within that
 * bracket will be charged at the corresponding rate.
 *
 * It iterates through the rates, applying each rate to the corresponding
 * distance bracket until the entire distance is covered.
 *
 * @param {number} distance - The total distance to calculate the subtotals for.
 * @param {DistanceRateType} distanceRate - The rate structure containing the
 * distance brackets and corresponding rates.
 * @returns {DistanceRateRangeSubtotal[]} An array of subtotals, each
 * representing a portion of the total distance and the rate applied to that
 * portion.
 */
function generateProgressiveRangeSubtotals(
  distance: number,
  distanceRate: DistanceRateType,
  isGstRegistered: boolean,
  variancePct: number | null | undefined,
): DistanceRateRangeSubtotal[] {
  const rangeSubtotals: DistanceRateRangeSubtotal[] = [];
  let remainingDistance: number = distance;

  // Iterate over each rate bracket in the distance rate
  for (const currentRange of distanceRate.rates) {
    if (remainingDistance <= 0) {
      break;
    }

    // Calculate the total distance covered by the current range
    // If bracketMax is -1, it means there is no upper limit, so we should use
    // the remaining distance
    const currentRateDistance =
      currentRange.bracketMax === -1
        ? remainingDistance
        : currentRange.bracketMax - currentRange.bracketMin;

    // Calculate the distance to apply the current rate to. If remaining
    // distance is less than the current rate distance, it means we are at the
    // last range and will just charge the remaining distance. Otherwise, we
    // will charge full extent of the current range (and will continue to the
    // next range)
    const rateDistance = Math.min(remainingDistance, currentRateDistance);

    // Calculate charges
    const { exclGst, gst, total } = calculateDistanceCharges(
      rateDistance,
      currentRange.rate,
      isGstRegistered,
      variancePct,
    );

    // Create a new subtotal for the current rate and distance
    const subtotal: DistanceRateRangeSubtotal = {
      rangeRateId: currentRange.id,
      chargeableDistance: rateDistance,
      chargeExclGst: exclGst,
      chargeGst: gst,
      chargeTotal: total,
    };
    rangeSubtotals.push(subtotal);

    // Subtract the applied distance from the remaining distance
    remainingDistance -= rateDistance;
  }

  return rangeSubtotals;
}

/**
 * Used to calculate the subtotals for a distance rate job, where the
 * RateBracketType is set to ABSOLUTE. In this case, we find the rate range that
 * the distance falls within and apply the rate to the entire distance.
 * @param distance - The total distance to calculate the subtotals for (rounding
 * already applied)
 * @param distanceRate - The rate structure containing the distance brackets and
 * corresponding rates.
 * @returns - An array of subtotals, each representing a portion of the total
 * distance and the rate applied to that portion. Should only contain one
 * element.
 */
function generateAbsoluteRangeSubtotals(
  distance: number,
  distanceRate: DistanceRateType,
  isGstRegistered: boolean,
  variancePct: number | null | undefined,
): DistanceRateRangeSubtotal[] {
  const rangeToApply = distanceRate.rates.find(
    (rate) =>
      (rate.bracketMin <= distance && rate.bracketMax >= distance) ||
      rate.bracketMax === -1,
  );

  if (!rangeToApply) {
    throw new Error(`No rate range found for distance: ${distance}`);
  }

  // Calculate charges
  const { exclGst, gst, total } = calculateDistanceCharges(
    distance,
    rangeToApply.rate,
    isGstRegistered,
    variancePct,
  );

  // Construct the result and return as list
  const result: DistanceRateRangeSubtotal = {
    rangeRateId: rangeToApply.id,
    chargeableDistance: distance,
    chargeExclGst: exclGst,
    chargeGst: gst,
    chargeTotal: total,
  };
  return [result];
}

/**
 * Constructs an object containing the distance charge breakdowns based on the
 * given rate distance, rate, and GST registration status.
 *
 * @param rateDistance - The distance for which the rate is being calculated.
 * @param rate - The rate per unit distance.
 * @param isGstRegistered - A boolean indicating whether GST is applicable.
 * @returns An object containing the charges excluding GST, the GST amount, and
 * the total charges.
 */
function calculateDistanceCharges(
  rateDistance: number,
  rate: number,
  isGstRegistered: boolean,
  variancePct: number | null | undefined,
): { exclGst: number; gst: number; total: number } {
  const variance = getPercentageOf(rate, variancePct ?? 0);
  const rateToApply = rate + variance;
  return calculateGstInclusiveTotals(
    rateDistance * rateToApply,
    isGstRegistered,
  );
}

/**
 * Generates an AdditionalTravel object based on the travelType, legTypeId, and
 * additional distances and durations. Depending on the travelType and
 * legTypeId, we grab different properties from the additionalDistances and
 * additionalDurations objects.
 * @param travelType - The type of travel, either 'PRE' job or 'POST' job
 * @param legTypeId - The ID of the leg type, either DEPOT, RETURN, or
 * NOT_APPLICABLE
 * @param additionalDistances - The additional distances object containing
 * depotToFirstLeg, depotToLastLeg, and returnToFirstPud in km
 * @param additionalDurations - The additional durations object containing
 * depotToFirstLeg, depotToLastLeg, and returnToFirstPud
 * @returns - An AdditionalTravel object containing the distance and duration
 * for the additional travel
 */
function generateAdditionalTravel(
  travelType: 'PRE' | 'POST',
  legTypeId: StartAndReturnLegsEnum,
  additionalDistances: LegDuration,
  additionalDurations: LegDuration,
): AdditionalTravel {
  const result: AdditionalTravel = {
    typeId: legTypeId,
    distanceInKm: 0,
    durationInMilliseconds: 0,
  };
  if (!legTypeId || legTypeId === StartAndReturnLegsEnum.NOT_APPLICABLE) {
    return result;
  }
  if (travelType === 'PRE' && legTypeId === StartAndReturnLegsEnum.DEPOT) {
    result.distanceInKm = RoundCurrencyValue(
      additionalDistances.depotToFirstLeg,
      3,
    );
    result.durationInMilliseconds = RoundCurrencyValue(
      additionalDurations.depotToFirstLeg,
      3,
    );
  }
  if (travelType === 'POST') {
    if (legTypeId === StartAndReturnLegsEnum.RETURN) {
      result.distanceInKm = RoundCurrencyValue(
        additionalDistances.returnToFirstPud,
        3,
      );
      result.durationInMilliseconds = RoundCurrencyValue(
        additionalDurations.returnToFirstPud,
        3,
      );
    } else {
      result.distanceInKm = RoundCurrencyValue(
        additionalDistances.depotToLastLeg,
        3,
      );
      result.durationInMilliseconds = RoundCurrencyValue(
        additionalDurations.depotToLastLeg,
        3,
      );
    }
  }
  return result;
}

/**
 * Returns the original distance of a job based on the given charge basis.
 *
 * @param {DistanceRateTravelSummary} distances - The distances for different charge bases.
 * @param {ChargeBasis} chargeBasis - The charge basis to determine which distance to return.
 * @returns {number} - The original distance based on the given charge basis.
 */
export function returnOriginalDistanceForChargeBasis(
  distances: DistanceRateTravelSummary,
  chargeBasis: ChargeBasis,
): number {
  if (chargeBasis === ChargeBasis.ANTICIPATED_ROUTE) {
    return distances.plannedRoute;
  } else if (chargeBasis === ChargeBasis.DISTANCE_TRAVELLED) {
    return distances.gpsData;
  } else {
    return distances.suburbCentres;
  }
}

/**
 * Returns the adjusted rate, minimum charge, and base freight charge for a
 * distance rate, applying the variance percentage if provided.
 *
 * @param foundRange - The found range rate object.
 * @param distanceRateType - The distance rate type object.
 * @param variancePct - The variance percentage to apply.
 * @returns An object containing the adjusted rate, minCharge, and
 * baseFreightCharge.
 */
export function getAdjustedDistanceRateValues(
  foundRange: { rate: number },
  distanceRateType: DistanceRateType,
  variancePct: number | null | undefined,
): { rateToApply: number; minCharge: number; baseFreightCharge: number } {
  const rateToApply =
    foundRange.rate + getPercentageOf(foundRange.rate, variancePct ?? 0);

  const minCharge =
    distanceRateType.minCharge +
    getPercentageOf(distanceRateType.minCharge, variancePct ?? 0);

  const baseFreightCharge = distanceRateType.baseFreightCharge
    ? distanceRateType.baseFreightCharge +
      getPercentageOf(distanceRateType.baseFreightCharge, variancePct ?? 0)
    : 0;

  return { rateToApply, minCharge, baseFreightCharge };
}
