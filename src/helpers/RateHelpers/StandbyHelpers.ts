// Pass in a PudDuration object and check what overlaps exist for all known

import {
  calculateGstInclusiveTotals,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { timeRateCalculation } from '@/helpers/RateHelpers/TimeRateHelpers';
import WorkDiarySummary from '@/interface-models/Driver/WorkDiary/WorkDiarySummary';
import PudDuration from '@/interface-models/Generic/Accounting/Standby/PudDuration';
import StandbyChargeBreakdown from '@/interface-models/Generic/Accounting/Standby/StandbyChargeBreakdown';
import StandbyDuration from '@/interface-models/Generic/Accounting/Standby/StandbyDuration';
import type JobDetails from '@/interface-models/Jobs/JobDetails';
import type PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';

// =============================================================================
// STANDBY DURATIONS
// =============================================================================
// Calculates individual leg standby durations along with overall standby duration.
export function calculateStandbyDurations(
  jobDetails: JobDetails,
  standbyRateApplies: boolean,
  workDiaryList: WorkDiarySummary[],
  rateEntityType: RateEntityType,
): StandbyDuration {
  const pudItems: PUDItem[] = jobDetails.pudItems;
  const standbyDuration = new StandbyDuration();

  for (const pud of pudItems) {
    if (!pud.isStandbyRate) {
      continue;
    }

    const arrival = jobDetails.returnSpecifiedEvent('ARRIVED', pud.pudId);
    const departed = jobDetails.returnSpecifiedEvent('FINISHED', pud.pudId);

    if (arrival && departed) {
      const duration = standbyRateApplies
        ? Math.max(0, departed.correctEventTime - arrival.correctEventTime)
        : 0;

      const pudDuration: PudDuration = new PudDuration(
        pud.pudId,
        duration,
        duration,
        arrival.correctEventTime,
        departed.correctEventTime,
        0,
      );
      const overlap = computeStandbyBreakOverlap(
        pudDuration,
        workDiaryList,
        rateEntityType,
      );
      pudDuration.breakOverlapDurationInMilliseconds = overlap;

      standbyDuration.durations.push(pudDuration);
      standbyDuration.totalOriginalDuration += duration;
      standbyDuration.totalEditedDuration += duration;
      standbyDuration.totalEditedDuration -= overlap;
    }
  }

  return standbyDuration;
}

// calculate the total charge for each individual standby leg
export function generateStandbyRateTotals(
  rate: number,
  multiplier: number,
  durations: PudDuration[],
  isGstRegistered: boolean,
): StandbyChargeBreakdown[] {
  const breakdown: StandbyChargeBreakdown[] = [];

  for (const pud of durations) {
    // We first remove any break overlap duration from our standby duration
    const standbyDuration =
      pud.editedDuration - pud.breakOverlapDurationInMilliseconds;
    const baseRate: number = RoundCurrencyValue(
      timeRateCalculation({
        rate,
        multiplier,
        durationInMs: standbyDuration > 0 ? standbyDuration : 0,
      }),
    );
    const totals = calculateGstInclusiveTotals(baseRate, isGstRegistered);

    breakdown.push(
      new StandbyChargeBreakdown(
        pud.pudId,
        totals.exclGst,
        totals.gst,
        totals.total,
      ),
    );
  }

  return breakdown;
}

// Work Diary records
export function computeStandbyBreakOverlap(
  standbyDuration: PudDuration,
  workDiaryList: WorkDiarySummary[],
  rateEntityType: RateEntityType,
): number {
  if (!workDiaryList || !workDiaryList.length) {
    return 0;
  }
  if (!standbyDuration.arrivalTime || !standbyDuration.departureTime) {
    return 0;
  }
  // Get overlap from each individual work diary then sum
  if (rateEntityType === RateEntityType.CLIENT) {
    // Filter for breaks that have the chargeClient boolean set to true
    return workDiaryList
      .filter((d) => !d.chargeClient)
      .map((d) =>
        d.returnOverlapMillis(
          standbyDuration.arrivalTime,
          standbyDuration.departureTime,
        ),
      )
      .reduce((prev, curr) => prev + curr);
  } else if (rateEntityType === RateEntityType.FLEET_ASSET) {
    // Filter for breaks that have the payFleetAsset boolean set to true
    return workDiaryList
      .filter((d) => !d.payFleetAsset)
      .map((d) =>
        d.returnOverlapMillis(
          standbyDuration.arrivalTime,
          standbyDuration.departureTime,
        ),
      )
      .reduce((prev, curr) => prev + curr);
  } else {
    return 0;
  }
}
